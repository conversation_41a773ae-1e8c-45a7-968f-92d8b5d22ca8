using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;
using Prism.Commands;

namespace DriverManagementSystem.ViewModels
{
    /// <summary>
    /// ViewModel للنافذة الجديدة Assignment
    /// </summary>
    public class AssignmentViewModel : INotifyPropertyChanged
    {
        private readonly FieldVisit _selectedVisit;

        public event PropertyChangedEventHandler PropertyChanged;

        // الخصائص الأساسية
        public FieldVisit SelectedVisit => _selectedVisit;
        public ObservableCollection<VisitorWithIndex> Visitors { get; set; }
        public ICommand PrintCommand { get; set; }
        public ICommand CloseCommand { get; set; }

        // خصائص محسوبة
        public string AssignmentNumber => $"تكليف رقم {_selectedVisit?.VisitNumber ?? "001"}/2025";
        public string MissionPurpose => _selectedVisit?.MissionPurpose ?? "زيارة ميدانية للمشاريع";
        public string DepartureDate => _selectedVisit?.DepartureDate.ToString("dd/MM/yyyy") ?? DateTime.Now.ToString("dd/MM/yyyy");
        public string ReturnDate => _selectedVisit?.ReturnDate.ToString("dd/MM/yyyy") ?? DateTime.Now.AddDays(3).ToString("dd/MM/yyyy");
        public string DurationText
        {
            get
            {
                if (_selectedVisit == null) return "3 أيام";
                var duration = (_selectedVisit.ReturnDate - _selectedVisit.DepartureDate).Days + 1;
                return duration == 1 ? "يوم واحد" : duration == 2 ? "يومان" : $"{duration} أيام";
            }
        }
        public string DriverName => _selectedVisit?.SelectedDrivers ?? "أحمد محمد الشامي";
        public string DriverPhoneNumber => "777123456";
        public string VehicleType => "تويوتا هايلكس";

        public AssignmentViewModel(FieldVisit selectedVisit)
        {
            _selectedVisit = selectedVisit;
            Visitors = new ObservableCollection<VisitorWithIndex>();

            // تهيئة البيانات
            InitializeData();

            // تهيئة الأوامر
            PrintCommand = new DelegateCommand(PrintAssignment);
            CloseCommand = new DelegateCommand<Window>(CloseWindow);
        }

        /// <summary>
        /// تهيئة البيانات
        /// </summary>
        private void InitializeData()
        {
            try
            {
                // إضافة بيانات تجريبية إذا لم تكن هناك بيانات حقيقية
                if (_selectedVisit?.Visitors?.Any() != true)
                {
                    Visitors.Add(new VisitorWithIndex
                    {
                        Index = 1,
                        Visitor = new FieldVisitor
                        {
                            OfficerName = "علي أحمد الغمدي",
                            OfficerRank = "مهندس",
                            OfficerCode = "ENG001",
                            PhoneNumber = "777123456"
                        }
                    });

                    Visitors.Add(new VisitorWithIndex
                    {
                        Index = 2,
                        Visitor = new FieldVisitor
                        {
                            OfficerName = "محمد سالم الشامي",
                            OfficerRank = "فني",
                            OfficerCode = "TEC001",
                            PhoneNumber = "777654321"
                        }
                    });
                }

                // إذا كان لدينا بيانات حقيقية، استخدمها
                if (_selectedVisit?.Visitors?.Any() == true)
                {
                    Visitors.Clear();
                    for (int i = 0; i < _selectedVisit.Visitors.Count; i++)
                    {
                        Visitors.Add(new VisitorWithIndex
                        {
                            Index = i + 1,
                            Visitor = _selectedVisit.Visitors[i]
                        });
                    }
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل {Visitors.Count} قائم بالزيارة في نافذة التكليف الجديدة");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تهيئة بيانات نافذة التكليف الجديدة: {ex.Message}");
            }
        }

        /// <summary>
        /// طباعة التكليف
        /// </summary>
        private void PrintAssignment()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🖨️ بدء طباعة التكليف الجديد...");

                // البحث عن نافذة التكليف الحالية
                var assignmentWindow = Application.Current.Windows.OfType<Views.AssignmentWindow>().FirstOrDefault();
                if (assignmentWindow == null)
                {
                    MessageBox.Show("لم يتم العثور على نافذة التكليف للطباعة", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // استخدام خدمة الطباعة المناسبة للنوافذ
                var printDialog = new System.Windows.Controls.PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    printDialog.PrintVisual(assignmentWindow, "تكليف الزيارة الميدانية");
                }

                System.Diagnostics.Debug.WriteLine("✅ تم إرسال التكليف للطباعة بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في طباعة التكليف: {ex.Message}");
                MessageBox.Show($"خطأ في طباعة التكليف: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void CloseWindow(Window window)
        {
            try
            {
                window?.Close();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إغلاق نافذة التكليف: {ex.Message}");
            }
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
