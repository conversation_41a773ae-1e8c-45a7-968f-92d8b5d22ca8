<Window x:Class="DriverManagementSystem.Views.AssignmentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="التكليف - نظام إدارة الزيارات الميدانية"
        Height="842"
        Width="595"
        MinHeight="800"
        MinWidth="580"
        WindowStartupLocation="CenterScreen"
        Background="White"
        FlowDirection="RightToLeft"
        FontFamily="Arial">

    <!-- استمارة طلب نزول ميداني - نفس التصميم الموجود -->
    <Grid Background="White" Margin="20">
        <StackPanel>

            <!-- Header مع الشعار والعنوان -->
            <Grid Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="100"/>
                </Grid.ColumnDefinitions>

                <!-- الشعار الأيسر -->
                <Border Grid.Column="0" Width="80" Height="80" Background="#E8F4FD"
                        BorderBrush="#1e3c72" BorderThickness="1" HorizontalAlignment="Center">
                    <TextBlock Text="الصندوق الاجتماعي للتنمية" FontSize="8" TextWrapping="Wrap"
                               HorizontalAlignment="Center" VerticalAlignment="Center" TextAlignment="Center"/>
                </Border>

                <!-- العنوان الوسط -->
                <Border Grid.Column="1" BorderBrush="Black" BorderThickness="2"
                        Background="White" Margin="10,20" Padding="10">
                    <TextBlock Text="استمارة طلب نزول ميداني" FontSize="16" FontWeight="Bold"
                               HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>

                <!-- الشعار الأيمن -->
                <Border Grid.Column="2" Width="80" Height="80" Background="#E8F4FD"
                        BorderBrush="#1e3c72" BorderThickness="1" HorizontalAlignment="Center">
                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                        <TextBlock Text="📋" FontSize="20" HorizontalAlignment="Center"/>
                        <TextBlock Text="الصندوق الاجتماعي للتنمية" FontSize="6" TextWrapping="Wrap"
                                   HorizontalAlignment="Center" TextAlignment="Center"/>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- الجدول الرئيسي -->
            <Border BorderBrush="Black" BorderThickness="2" Margin="0,0,0,20">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- الصف الأول: تاريخ الطلب والاستمارة -->
                    <Grid Grid.Row="0" Background="#F0F8FF">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- تاريخ الطلب -->
                        <Border Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,1" Padding="10">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📅 تاريخ الطلب:" FontWeight="Bold" Margin="0,0,10,0"/>
                                <TextBlock Text="{Binding DepartureDate, FallbackValue='21/07/2025'}" />
                            </StackPanel>
                        </Border>

                        <!-- رقم الاستمارة -->
                        <Border Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,0,1" Padding="10">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📋 رقم الاستمارة:" FontWeight="Bold" Margin="0,0,10,0"/>
                                <TextBlock Text="{Binding AssignmentNumber, FallbackValue='1300-2025/07/21'}" />
                            </StackPanel>
                        </Border>
                    </Grid>

                    <!-- الصف الثاني: البيانات الأساسية للزيارة -->
                    <Border Grid.Row="1" BorderBrush="Black" BorderThickness="0,0,0,1" Padding="15" Background="White">
                        <StackPanel>
                            <TextBlock Text="البيانات الأساسية للزيارة" FontWeight="Bold" FontSize="14"
                                       HorizontalAlignment="Center" Margin="0,0,0,15"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- الغرض -->
                                <TextBlock Grid.Row="0" Grid.Column="0" Text="الغرض:" FontWeight="Bold" Margin="0,5"/>
                                <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding MissionPurpose, FallbackValue='زيارة ميدانية'}" Margin="10,5"/>

                                <!-- تاريخ المغادرة -->
                                <TextBlock Grid.Row="1" Grid.Column="0" Text="تاريخ المغادرة:" FontWeight="Bold" Margin="0,5"/>
                                <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding DepartureDate, FallbackValue='21/07/2025'}" Margin="10,5"/>

                                <!-- تاريخ العودة -->
                                <TextBlock Grid.Row="2" Grid.Column="0" Text="تاريخ العودة:" FontWeight="Bold" Margin="0,5"/>
                                <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding ReturnDate, FallbackValue='24/07/2025'}" Margin="10,5"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- الصف الثالث: مهمة النزول -->
                    <Border Grid.Row="2" BorderBrush="Black" BorderThickness="0,0,0,1" Padding="15" Background="#F9F9F9">
                        <StackPanel>
                            <TextBlock Text="مهمة النزول" FontWeight="Bold" FontSize="14"
                                       HorizontalAlignment="Center" Margin="0,0,0,10"/>
                            <TextBox Text="المتابعة الميدانية وتقييم سير العمل في المشاريع والتأكد من الالتزام بالمعايير المطلوبة"
                                     Height="60" TextWrapping="Wrap" VerticalScrollBarVisibility="Auto"
                                     BorderBrush="#CCCCCC" BorderThickness="1" Padding="5"/>
                        </StackPanel>
                    </Border>

                    <!-- الصف الرابع: القائمين بالزيارة -->
                    <Border Grid.Row="3" BorderBrush="Black" BorderThickness="0,0,0,0" Padding="15" Background="White">
                        <StackPanel>
                            <TextBlock Text="القائمين بالزيارة" FontWeight="Bold" FontSize="14"
                                       HorizontalAlignment="Center" Margin="0,0,0,10"/>
                            <TextBox Height="80" TextWrapping="Wrap" VerticalScrollBarVisibility="Auto"
                                     BorderBrush="#CCCCCC" BorderThickness="1" Padding="5"
                                     Text="1. علي أحمد الغمدي - مهندس&#x0a;2. محمد سالم الشامي - فني"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </Border>

            <!-- المشرف وخط السير -->
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- المشرف -->
                <Border Grid.Column="0" BorderBrush="Black" BorderThickness="1" Padding="10" Margin="0,0,5,0">
                    <StackPanel>
                        <TextBlock Text="المشرف وخط السير" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="خط السير:" FontWeight="Bold" Margin="0,5"/>
                        <TextBox Height="40" BorderBrush="#CCCCCC" BorderThickness="1" Padding="5"
                                 Text="صنعاء - ذمار - البيضاء - صنعاء"/>
                    </StackPanel>
                </Border>

                <!-- المدير -->
                <Border Grid.Column="1" BorderBrush="Black" BorderThickness="1" Padding="10" Margin="5,0,0,0">
                    <StackPanel>
                        <TextBlock Text="المدير" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="التوقيع:" FontWeight="Bold" Margin="0,5"/>
                        <Border Height="40" BorderBrush="#CCCCCC" BorderThickness="1" Background="#F9F9F9"/>
                    </StackPanel>
                </Border>
            </Grid>

        </StackPanel>
    </Grid>

</Window>
