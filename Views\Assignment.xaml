<Window x:Class="DriverManagementSystem.Views.AssignmentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="التكليف - نظام إدارة الزيارات الميدانية"
        Height="700"
        Width="900"
        MinHeight="600"
        MinWidth="800"
        WindowStartupLocation="CenterScreen"
        Background="#F5F5F5"
        FlowDirection="RightToLeft"
        FontFamily="Arial"
        ResizeMode="CanResize">

    <!-- تخطيط معكوس - الإطار الجانبي يسار والمحتوى يمين -->
    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- شريط الأزرار العلوي -->
        <Border Grid.Row="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1" Padding="10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                <Button Name="PrintButton" Content="طباعة" Background="#2196F3" Foreground="White" 
                        Padding="15,8" Margin="5,0" FontSize="14" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="PrintButton_Click"/>
            </StackPanel>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- الشريط الجانبي الأيسر -->
            <Border Grid.Column="0" Background="#34495E" BorderBrush="#2C3E50" BorderThickness="0,0,1,0">
                <StackPanel Margin="0,20,0,0">
                    <!-- عنوان الشريط الجانبي -->
                    <TextBlock Text="التنقل" FontSize="16" FontWeight="Bold" Foreground="White" 
                               HorizontalAlignment="Center" Margin="0,0,0,20"/>

                    <!-- أزرار التنقل -->
                    <Button Content="الرئيسية" Background="Transparent" Foreground="White" 
                            BorderThickness="0" Padding="15,10" FontSize="14" 
                            HorizontalAlignment="Stretch" Cursor="Hand" Margin="5"/>
                    
                    <Button Content="الزيارات" Background="Transparent" Foreground="White" 
                            BorderThickness="0" Padding="15,10" FontSize="14" 
                            HorizontalAlignment="Stretch" Cursor="Hand" Margin="5"/>
                    
                    <Button Content="التقارير" Background="Transparent" Foreground="White" 
                            BorderThickness="0" Padding="15,10" FontSize="14" 
                            HorizontalAlignment="Stretch" Cursor="Hand" Margin="5"/>
                </StackPanel>
            </Border>

            <!-- منطقة المحتوى الرئيسي -->
            <ScrollViewer Grid.Column="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                <StackPanel Margin="15,5,5,5">
                    
                    <!-- الهيدر -->
                    <Border Background="White" BorderBrush="#E0E0E0" BorderThickness="1" Margin="0,0,0,10">
                        <Grid Margin="20,15,15,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <!-- معلومات الشركة -->
                            <StackPanel Grid.Column="0" HorizontalAlignment="Right">
                                <TextBlock Text="Social Fund For Development" FontSize="12" Foreground="#666" 
                                           HorizontalAlignment="Right" FontFamily="Arial"/>
                                <TextBlock Text="Republic OF YEMEN" FontSize="10" Foreground="#666" 
                                           HorizontalAlignment="Right" FontFamily="Arial"/>
                                <TextBlock Text="Presidency of Council of Ministers" FontSize="8" Foreground="#666" 
                                           HorizontalAlignment="Right" FontFamily="Arial" Margin="0,5,0,0"/>
                                <TextBlock Text="Dhamar &amp;Ibb&amp; Branch" FontSize="8" Foreground="#666" 
                                           HorizontalAlignment="Right" FontFamily="Arial"/>
                            </StackPanel>

                            <!-- الشعار -->
                            <StackPanel Grid.Column="1" HorizontalAlignment="Left" VerticalAlignment="Center">
                                <Border Background="#2196F3" Width="60" Height="60" CornerRadius="5">
                                    <TextBlock Text="ص ت ي" FontSize="20" FontWeight="Bold" Foreground="White" 
                                               HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <TextBlock Text="الصندوق الاجتماعي للتنمية" FontSize="10" HorizontalAlignment="Center" 
                                           Margin="0,5,0,0" FontWeight="Bold"/>
                                <TextBlock Text="فرع ذمار وإب" FontSize="8" HorizontalAlignment="Center" 
                                           Foreground="#666"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- منطقة المحتوى -->
                    <Border BorderBrush="#E0E0E0" BorderThickness="1" Background="White" MinHeight="600" Padding="30" HorizontalAlignment="Stretch" FlowDirection="RightToLeft">
                        <StackPanel FlowDirection="RightToLeft">

                            <!-- عنوان التكليف -->
                            <TextBlock Text="تكليف" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,20,0,30"/>

                            <!-- معلومات الزيارة -->
                            <Grid Margin="0,0,0,30">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" HorizontalAlignment="Right">
                                    <TextBlock Text="رقم الزيارة: 001" FontSize="14" Margin="0,5"/>
                                    <TextBlock Text="تاريخ الزيارة: ___________" FontSize="14" Margin="0,5"/>
                                    <TextBlock Text="مدة الزيارة: ___________" FontSize="14" Margin="0,5"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" HorizontalAlignment="Right">
                                    <TextBlock Text="المشروع: ___________" FontSize="14" Margin="0,5"/>
                                    <TextBlock Text="الموقع: ___________" FontSize="14" Margin="0,5"/>
                                    <TextBlock Text="المنطقة: ___________" FontSize="14" Margin="0,5"/>
                                </StackPanel>
                            </Grid>

                            <!-- جدول السائقين -->
                            <TextBlock Text="السائقون المكلفون:" FontSize="16" FontWeight="Bold" Margin="0,20,0,10" HorizontalAlignment="Right"/>

                            <Border BorderBrush="#CCCCCC" BorderThickness="1" Margin="0,0,0,30" FlowDirection="RightToLeft">
                                <Grid FlowDirection="RightToLeft">
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="50"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- رأس الجدول -->
                                    <Border Grid.Row="0" Grid.Column="0" BorderBrush="#CCCCCC" BorderThickness="0,0,1,1" Background="#F5F5F5" Padding="5">
                                        <TextBlock Text="م" FontWeight="Bold" HorizontalAlignment="Center"/>
                                    </Border>
                                    <Border Grid.Row="0" Grid.Column="1" BorderBrush="#CCCCCC" BorderThickness="0,0,1,1" Background="#F5F5F5" Padding="5">
                                        <TextBlock Text="اسم السائق" FontWeight="Bold" HorizontalAlignment="Center"/>
                                    </Border>
                                    <Border Grid.Row="0" Grid.Column="2" BorderBrush="#CCCCCC" BorderThickness="0,0,1,1" Background="#F5F5F5" Padding="5">
                                        <TextBlock Text="رقم الهاتف" FontWeight="Bold" HorizontalAlignment="Center"/>
                                    </Border>
                                    <Border Grid.Row="0" Grid.Column="3" BorderBrush="#CCCCCC" BorderThickness="0,0,0,1" Background="#F5F5F5" Padding="5">
                                        <TextBlock Text="نوع المركبة" FontWeight="Bold" HorizontalAlignment="Center"/>
                                    </Border>

                                    <!-- صفوف البيانات -->
                                    <Border Grid.Row="1" Grid.Column="0" BorderBrush="#CCCCCC" BorderThickness="0,0,1,1" Padding="5">
                                        <TextBlock Text="1" HorizontalAlignment="Center"/>
                                    </Border>
                                    <Border Grid.Row="1" Grid.Column="1" BorderBrush="#CCCCCC" BorderThickness="0,0,1,1" Padding="5">
                                        <TextBlock Text="___________________" HorizontalAlignment="Center"/>
                                    </Border>
                                    <Border Grid.Row="1" Grid.Column="2" BorderBrush="#CCCCCC" BorderThickness="0,0,1,1" Padding="5">
                                        <TextBlock Text="___________________" HorizontalAlignment="Center"/>
                                    </Border>
                                    <Border Grid.Row="1" Grid.Column="3" BorderBrush="#CCCCCC" BorderThickness="0,0,0,1" Padding="5">
                                        <TextBlock Text="___________________" HorizontalAlignment="Center"/>
                                    </Border>

                                    <Border Grid.Row="2" Grid.Column="0" BorderBrush="#CCCCCC" BorderThickness="0,0,1,1" Padding="5">
                                        <TextBlock Text="2" HorizontalAlignment="Center"/>
                                    </Border>
                                    <Border Grid.Row="2" Grid.Column="1" BorderBrush="#CCCCCC" BorderThickness="0,0,1,1" Padding="5">
                                        <TextBlock Text="___________________" HorizontalAlignment="Center"/>
                                    </Border>
                                    <Border Grid.Row="2" Grid.Column="2" BorderBrush="#CCCCCC" BorderThickness="0,0,1,1" Padding="5">
                                        <TextBlock Text="___________________" HorizontalAlignment="Center"/>
                                    </Border>
                                    <Border Grid.Row="2" Grid.Column="3" BorderBrush="#CCCCCC" BorderThickness="0,0,0,1" Padding="5">
                                        <TextBlock Text="___________________" HorizontalAlignment="Center"/>
                                    </Border>

                                    <Border Grid.Row="3" Grid.Column="0" BorderBrush="#CCCCCC" BorderThickness="0,0,1,1" Padding="5">
                                        <TextBlock Text="3" HorizontalAlignment="Center"/>
                                    </Border>
                                    <Border Grid.Row="3" Grid.Column="1" BorderBrush="#CCCCCC" BorderThickness="0,0,1,1" Padding="5">
                                        <TextBlock Text="___________________" HorizontalAlignment="Center"/>
                                    </Border>
                                    <Border Grid.Row="3" Grid.Column="2" BorderBrush="#CCCCCC" BorderThickness="0,0,1,1" Padding="5">
                                        <TextBlock Text="___________________" HorizontalAlignment="Center"/>
                                    </Border>
                                    <Border Grid.Row="3" Grid.Column="3" BorderBrush="#CCCCCC" BorderThickness="0,0,0,1" Padding="5">
                                        <TextBlock Text="___________________" HorizontalAlignment="Center"/>
                                    </Border>

                                    <Border Grid.Row="4" Grid.Column="0" BorderBrush="#CCCCCC" BorderThickness="0,0,1,0" Padding="5">
                                        <TextBlock Text="4" HorizontalAlignment="Center"/>
                                    </Border>
                                    <Border Grid.Row="4" Grid.Column="1" BorderBrush="#CCCCCC" BorderThickness="0,0,1,0" Padding="5">
                                        <TextBlock Text="___________________" HorizontalAlignment="Center"/>
                                    </Border>
                                    <Border Grid.Row="4" Grid.Column="2" BorderBrush="#CCCCCC" BorderThickness="0,0,1,0" Padding="5">
                                        <TextBlock Text="___________________" HorizontalAlignment="Center"/>
                                    </Border>
                                    <Border Grid.Row="4" Grid.Column="3" BorderBrush="#CCCCCC" BorderThickness="0,0,0,0" Padding="5">
                                        <TextBlock Text="___________________" HorizontalAlignment="Center"/>
                                    </Border>
                                </Grid>
                            </Border>

                            <!-- ملاحظات -->
                            <TextBlock Text="ملاحظات:" FontSize="16" FontWeight="Bold" Margin="0,20,0,10" HorizontalAlignment="Right"/>
                            <Border BorderBrush="#CCCCCC" BorderThickness="1" MinHeight="100" Padding="10" Margin="0,0,0,30">
                                <TextBlock Text="________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________" 
                                           TextWrapping="Wrap" FontSize="12"/>
                            </Border>

                            <!-- التوقيعات -->
                            <Grid Margin="0,30,0,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                    <TextBlock Text="توقيع المسؤول" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,30"/>
                                    <TextBlock Text="____________________" HorizontalAlignment="Center"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                    <TextBlock Text="التاريخ" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,30"/>
                                    <TextBlock Text="____________________" HorizontalAlignment="Center"/>
                                </StackPanel>
                            </Grid>

                        </StackPanel>
                    </Border>

                </StackPanel>
            </ScrollViewer>
        </Grid>
    </Grid>
</Window>
