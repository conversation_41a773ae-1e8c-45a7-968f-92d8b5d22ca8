<Window x:Class="DriverManagementSystem.Views.AssignmentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="التكليف - نظام إدارة الزيارات الميدانية"
        Height="842"
        Width="595"
        MinHeight="800"
        MinWidth="580"
        WindowStartupLocation="CenterScreen"
        Background="White"
        FlowDirection="RightToLeft"
        FontFamily="Arial">

    <Window.Resources>
        <ResourceDictionary>
            <!-- Converter للتحقق من وجود الصور -->
            <BooleanToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
        </ResourceDictionary>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#1e3c72" CornerRadius="5" Padding="15,12" Margin="0,0,0,20">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="315" ShadowDepth="3"
                                  Opacity="0.25" BlurRadius="6"/>
            </Border.Effect>
            <StackPanel HorizontalAlignment="Center">
                <TextBlock Text="📋 تكليف رسمي 📋" FontSize="18" FontWeight="Bold"
                         Foreground="White" HorizontalAlignment="Center"/>
                <TextBlock Text="{Binding AssignmentNumber}" FontSize="12"
                         Foreground="#E8F4FD" HorizontalAlignment="Center" Margin="0,4,0,0"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="10">

                <!-- معلومات الزيارة -->
                <Border Background="#F8F9FA" CornerRadius="8" Padding="20" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="📋 معلومات الزيارة" FontSize="16" FontWeight="Bold"
                                 Foreground="#1e3c72" Margin="0,0,0,15"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="الغرض من المهمة:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding MissionPurpose}" Margin="10,5"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="تاريخ المغادرة:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding DepartureDate}" Margin="10,5"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="تاريخ العودة:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding ReturnDate}" Margin="10,5"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- القائمين بالزيارة -->
                <Border Background="#F8F9FA" CornerRadius="8" Padding="20" Margin="0,0,0,15">
                    <StackPanel>
                        <TextBlock Text="👥 القائمين بالزيارة" FontSize="16" FontWeight="Bold"
                                 Foreground="#1e3c72" Margin="0,0,0,15"/>

                        <DataGrid ItemsSource="{Binding Visitors}" AutoGenerateColumns="False"
                                CanUserAddRows="False" CanUserDeleteRows="False"
                                GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                                Background="White" RowBackground="White" AlternatingRowBackground="#F8F9FA">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="م" Binding="{Binding Index}" Width="50" IsReadOnly="True"/>
                                <DataGridTextColumn Header="الرتبة" Binding="{Binding Visitor.OfficerRank}" Width="100" IsReadOnly="True"/>
                                <DataGridTextColumn Header="الاسم" Binding="{Binding Visitor.OfficerName}" Width="*" IsReadOnly="True"/>
                                <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding Visitor.PhoneNumber}" Width="120" IsReadOnly="True"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </StackPanel>
                </Border>

                <!-- معلومات السائق والمركبة -->
                <Border Background="#F8F9FA" CornerRadius="8" Padding="20">
                    <StackPanel>
                        <TextBlock Text="🚗 معلومات السائق والمركبة" FontSize="16" FontWeight="Bold"
                                 Foreground="#1e3c72" Margin="0,0,0,15"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم السائق:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding DriverName}" Margin="10,5"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="رقم الهاتف:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding DriverPhoneNumber}" Margin="10,5"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="نوع المركبة:" FontWeight="Bold" Margin="0,5"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding VehicleType}" Margin="10,5"/>
                        </Grid>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- Footer Buttons -->
        <Border Grid.Row="2" Background="#F8F9FA" CornerRadius="5" Padding="15" Margin="0,20,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="🖨️ طباعة" Command="{Binding PrintCommand}"
                      Background="#28a745" Foreground="White" Padding="20,10" Margin="10,0"
                      FontSize="14" FontWeight="Bold" MinWidth="120">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                              CornerRadius="5"
                                              Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>
                <Button Content="❌ إغلاق" Click="CloseButton_Click"
                      Background="#dc3545" Foreground="White" Padding="20,10" Margin="10,0"
                      FontSize="14" FontWeight="Bold" MinWidth="120">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}"
                                              CornerRadius="5"
                                              Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>
            </StackPanel>
        </Border>

    </Grid>
</Window>
