<Window x:Class="DriverManagementSystem.Views.AssignmentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="التكليف - نظام إدارة الزيارات الميدانية"
        Height="800"
        Width="1200"
        MinHeight="700"
        MinWidth="1000"
        WindowStartupLocation="CenterScreen"
        Background="#F5F5F5"
        FlowDirection="RightToLeft"
        FontFamily="Arial"
        WindowState="Maximized">

    <!-- تخطيط معكوس - الإطار الجانبي يسار والمحتوى يمين -->
    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="300"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Header الأزرق مع الأزرار -->
        <Border Grid.Row="0" Grid.ColumnSpan="2" Background="#2E86C1" Padding="15,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- الأزرار اليسار -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Button Content="❌ إغلاق" Click="CloseButton_Click"
                          Background="#E74C3C" Foreground="White" Padding="15,8" Margin="5,0"
                          FontSize="12" FontWeight="Bold" BorderThickness="0"/>
                    <Button Content="📋 التكليف"
                          Background="#34495E" Foreground="White" Padding="15,8" Margin="5,0"
                          FontSize="12" FontWeight="Bold" BorderThickness="0"/>
                    <Button Content="📄 توثيق الرسائل"
                          Background="#27AE60" Foreground="White" Padding="15,8" Margin="5,0"
                          FontSize="12" FontWeight="Bold" BorderThickness="0"/>
                </StackPanel>

                <!-- العنوان الوسط -->
                <TextBlock Grid.Column="1" Text="تقرير النزول الميداني"
                         FontSize="18" FontWeight="Bold" Foreground="White"
                         HorizontalAlignment="Center" VerticalAlignment="Center"/>

                <!-- أيقونة التقرير -->
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <TextBlock Text="📋" FontSize="24" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- الإطار الجانبي الأيسر -->
        <Border Grid.Row="1" Grid.Column="0" Background="White" BorderBrush="#BDC3C7"
                BorderThickness="0,0,2,0" Margin="20,20,0,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- عنوان الإطار الجانبي -->
                <Border Grid.Row="0" Background="#3498DB" Padding="15,10">
                    <StackPanel>
                        <TextBlock Text="📋 رقم الزيارة" FontSize="14" FontWeight="Bold"
                                   Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="🔢" FontSize="20" Foreground="White"
                                   HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- رقم الزيارة فقط -->
                <Border Grid.Row="1" Background="#ECF0F1" Margin="10" Padding="15" CornerRadius="5">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <TextBlock Text="رقم الزيارة" FontWeight="Bold" FontSize="12"
                                   Foreground="#7F8C8D" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding SelectedVisit.VisitNumber, FallbackValue='911-1300'}"
                                   FontSize="24" FontWeight="Bold" Foreground="#2C3E50"
                                   HorizontalAlignment="Center" Margin="0,10"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي - المنطقة الفارغة -->
        <Grid Grid.Row="1" Grid.Column="1" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- زر الطباعة فقط -->
            <Border Grid.Row="0" Background="#ECF0F1" Padding="10" Margin="0,0,0,10" CornerRadius="5">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button Content="🖨️ طباعة"
                          Background="#27AE60" Foreground="White" Padding="20,10"
                          FontSize="14" FontWeight="Bold" BorderThickness="0"/>
                </StackPanel>
            </Border>

            <!-- المنطقة الفارغة للتصميم المستقبلي -->
            <Border Grid.Row="1" BorderBrush="#BDC3C7" BorderThickness="2" Background="White" CornerRadius="5">
                <Grid>
                    <TextBlock Text="منطقة التصميم" FontSize="18" Foreground="#BDC3C7"
                               HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Grid>
            </Border>
        </Grid>
    </Grid>

</Window>
