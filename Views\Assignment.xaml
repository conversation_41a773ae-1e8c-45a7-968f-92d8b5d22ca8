<Window x:Class="DriverManagementSystem.Views.AssignmentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="التكليف - نظام إدارة الزيارات الميدانية"
        Height="800"
        Width="1200"
        MinHeight="700"
        MinWidth="1000"
        WindowStartupLocation="CenterScreen"
        Background="#F5F5F5"
        FlowDirection="RightToLeft"
        FontFamily="Arial"
        WindowState="Maximized">

    <!-- نفس تخطيط الشاشة الموجودة في الصورة -->
    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="300"/>
        </Grid.ColumnDefinitions>

        <!-- Header الأزرق مع الأزرار -->
        <Border Grid.Row="0" Grid.ColumnSpan="2" Background="#2E86C1" Padding="15,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- الأزرار اليسار -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Button Content="❌ إغلاق" Click="CloseButton_Click"
                          Background="#E74C3C" Foreground="White" Padding="15,8" Margin="5,0"
                          FontSize="12" FontWeight="Bold" BorderThickness="0"/>
                    <Button Content="📋 التكليف"
                          Background="#34495E" Foreground="White" Padding="15,8" Margin="5,0"
                          FontSize="12" FontWeight="Bold" BorderThickness="0"/>
                    <Button Content="📄 توثيق الرسائل"
                          Background="#27AE60" Foreground="White" Padding="15,8" Margin="5,0"
                          FontSize="12" FontWeight="Bold" BorderThickness="0"/>
                </StackPanel>

                <!-- العنوان الوسط -->
                <TextBlock Grid.Column="1" Text="تقرير النزول الميداني"
                         FontSize="18" FontWeight="Bold" Foreground="White"
                         HorizontalAlignment="Center" VerticalAlignment="Center"/>

                <!-- أيقونة التقرير -->
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <TextBlock Text="📋" FontSize="24" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي - التقرير -->
        <Grid Grid.Row="1" Grid.Column="0" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- أزرار التحكم -->
            <Border Grid.Row="0" Background="#ECF0F1" Padding="10" Margin="0,0,0,10" CornerRadius="5">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                    <Button Content="📝 تحرير العقد"
                          Background="#95A5A6" Foreground="White" Padding="12,6" Margin="5,0"
                          FontSize="11" FontWeight="Bold" BorderThickness="0"/>
                    <Button Content="🖨️ طباعة العقد"
                          Background="#27AE60" Foreground="White" Padding="12,6" Margin="5,0"
                          FontSize="11" FontWeight="Bold" BorderThickness="0"/>
                    <Border Background="#E8F6F3" BorderBrush="#27AE60" BorderThickness="1"
                          Padding="8,4" Margin="20,0,0,0" CornerRadius="3">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔢" FontSize="10" Margin="0,0,5,0"/>
                            <TextBlock Text="رقم الزيارة: " FontSize="10" FontWeight="Bold"/>
                            <TextBlock Text="{Binding SelectedVisit.VisitNumber, FallbackValue='911-1300'}" FontSize="10"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Border>

            <!-- التقرير بإطار -->
            <Border Grid.Row="1" BorderBrush="#BDC3C7" BorderThickness="2" Background="White" CornerRadius="5">
                <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
                    <StackPanel Margin="30" Background="White">

                        <!-- Header مع الشعار والعنوان -->
                        <Grid Margin="0,0,0,20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="100"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="100"/>
                            </Grid.ColumnDefinitions>

                            <!-- الشعار الأيسر -->
                            <Border Grid.Column="0" Width="80" Height="80" Background="#E8F4FD"
                                    BorderBrush="#1e3c72" BorderThickness="1" HorizontalAlignment="Center">
                                <TextBlock Text="الصندوق الاجتماعي للتنمية" FontSize="8" TextWrapping="Wrap"
                                           HorizontalAlignment="Center" VerticalAlignment="Center" TextAlignment="Center"/>
                            </Border>

                            <!-- العنوان الوسط -->
                            <Border Grid.Column="1" BorderBrush="Black" BorderThickness="2"
                                    Background="White" Margin="10,20" Padding="10">
                                <TextBlock Text="استمارة طلب نزول ميداني" FontSize="16" FontWeight="Bold"
                                           HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>

                            <!-- الشعار الأيمن -->
                            <Border Grid.Column="2" Width="80" Height="80" Background="#E8F4FD"
                                    BorderBrush="#1e3c72" BorderThickness="1" HorizontalAlignment="Center">
                                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                                    <TextBlock Text="📋" FontSize="20" HorizontalAlignment="Center"/>
                                    <TextBlock Text="الصندوق الاجتماعي للتنمية" FontSize="6" TextWrapping="Wrap"
                                               HorizontalAlignment="Center" TextAlignment="Center"/>
                                </StackPanel>
                            </Border>
                        </Grid>

                        <!-- الجدول الرئيسي -->
                        <Border BorderBrush="Black" BorderThickness="2" Margin="0,0,0,20">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- الصف الأول: تاريخ الطلب والاستمارة -->
                                <Grid Grid.Row="0" Background="#F0F8FF">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- تاريخ الطلب -->
                                    <Border Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,1" Padding="10">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="📅 تاريخ الطلب:" FontWeight="Bold" Margin="0,0,10,0"/>
                                            <TextBlock Text="{Binding DepartureDate, FallbackValue='21/07/2025'}" />
                                        </StackPanel>
                                    </Border>

                                    <!-- رقم الاستمارة -->
                                    <Border Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,0,1" Padding="10">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="📋 رقم الاستمارة:" FontWeight="Bold" Margin="0,0,10,0"/>
                                            <TextBlock Text="{Binding AssignmentNumber, FallbackValue='1300-2025/07/21'}" />
                                        </StackPanel>
                                    </Border>
                                </Grid>

                                <!-- الصف الثاني: البيانات الأساسية للزيارة -->
                                <Border Grid.Row="1" BorderBrush="Black" BorderThickness="0,0,0,1" Padding="15" Background="White">
                                    <StackPanel>
                                        <TextBlock Text="البيانات الأساسية للزيارة" FontWeight="Bold" FontSize="14"
                                                   HorizontalAlignment="Center" Margin="0,0,0,15"/>

                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                            </Grid.RowDefinitions>

                                            <!-- الغرض -->
                                            <TextBlock Grid.Row="0" Grid.Column="0" Text="الغرض:" FontWeight="Bold" Margin="0,5"/>
                                            <TextBlock Grid.Row="0" Grid.Column="1" Text="{Binding MissionPurpose, FallbackValue='زيارة ميدانية'}" Margin="10,5"/>

                                            <!-- تاريخ المغادرة -->
                                            <TextBlock Grid.Row="1" Grid.Column="0" Text="تاريخ المغادرة:" FontWeight="Bold" Margin="0,5"/>
                                            <TextBlock Grid.Row="1" Grid.Column="1" Text="{Binding DepartureDate, FallbackValue='21/07/2025'}" Margin="10,5"/>

                                            <!-- تاريخ العودة -->
                                            <TextBlock Grid.Row="2" Grid.Column="0" Text="تاريخ العودة:" FontWeight="Bold" Margin="0,5"/>
                                            <TextBlock Grid.Row="2" Grid.Column="1" Text="{Binding ReturnDate, FallbackValue='24/07/2025'}" Margin="10,5"/>
                                        </Grid>
                                    </StackPanel>
                                </Border>

                                <!-- الصف الثالث: مهمة النزول -->
                                <Border Grid.Row="2" BorderBrush="Black" BorderThickness="0,0,0,1" Padding="15" Background="#F9F9F9">
                                    <StackPanel>
                                        <TextBlock Text="مهمة النزول" FontWeight="Bold" FontSize="14"
                                                   HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                        <TextBox Text="المتابعة الميدانية وتقييم سير العمل في المشاريع والتأكد من الالتزام بالمعايير المطلوبة"
                                                 Height="60" TextWrapping="Wrap" VerticalScrollBarVisibility="Auto"
                                                 BorderBrush="#CCCCCC" BorderThickness="1" Padding="5"/>
                                    </StackPanel>
                                </Border>

                                <!-- الصف الرابع: القائمين بالزيارة -->
                                <Border Grid.Row="3" BorderBrush="Black" BorderThickness="0,0,0,0" Padding="15" Background="White">
                                    <StackPanel>
                                        <TextBlock Text="القائمين بالزيارة" FontWeight="Bold" FontSize="14"
                                                   HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                        <TextBox Height="80" TextWrapping="Wrap" VerticalScrollBarVisibility="Auto"
                                                 BorderBrush="#CCCCCC" BorderThickness="1" Padding="5"
                                                 Text="1. علي أحمد الغمدي - مهندس&#x0a;2. محمد سالم الشامي - فني"/>
                                    </StackPanel>
                                </Border>
                            </Grid>
                        </Border>

                        <!-- المشرف وخط السير -->
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- المشرف -->
                            <Border Grid.Column="0" BorderBrush="Black" BorderThickness="1" Padding="10" Margin="0,0,5,0">
                                <StackPanel>
                                    <TextBlock Text="المشرف وخط السير" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                    <TextBlock Text="خط السير:" FontWeight="Bold" Margin="0,5"/>
                                    <TextBox Height="40" BorderBrush="#CCCCCC" BorderThickness="1" Padding="5"
                                             Text="صنعاء - ذمار - البيضاء - صنعاء"/>
                                </StackPanel>
                            </Border>

                            <!-- المدير -->
                            <Border Grid.Column="1" BorderBrush="Black" BorderThickness="1" Padding="10" Margin="5,0,0,0">
                                <StackPanel>
                                    <TextBlock Text="المدير" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                    <TextBlock Text="التوقيع:" FontWeight="Bold" Margin="0,5"/>
                                    <Border Height="40" BorderBrush="#CCCCCC" BorderThickness="1" Background="#F9F9F9"/>
                                </StackPanel>
                            </Border>
                        </Grid>

                    </StackPanel>
                </ScrollViewer>
            </Border>
        </Grid>

        <!-- الإطار الجانبي الأيمن -->
        <Border Grid.Row="1" Grid.Column="1" Background="White" BorderBrush="#BDC3C7"
                BorderThickness="2,0,0,0" Margin="0,20,20,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- عنوان الإطار الجانبي -->
                <Border Grid.Row="0" Background="#3498DB" Padding="15,10">
                    <StackPanel>
                        <TextBlock Text="📋 اختيار الزيارة الميدانية" FontSize="14" FontWeight="Bold"
                                   Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="📊" FontSize="20" Foreground="White"
                                   HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- قائمة الزيارات -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="10">
                    <StackPanel>
                        <!-- زيارة 1 -->
                        <Border Background="#ECF0F1" BorderBrush="#BDC3C7" BorderThickness="1"
                                Margin="0,5" Padding="10" CornerRadius="3">
                            <StackPanel>
                                <TextBlock Text="التاريخ" FontWeight="Bold" FontSize="10" Foreground="#7F8C8D"/>
                                <TextBlock Text="21/07/2025 الموافق" FontSize="11" Margin="0,2"/>
                                <TextBlock Text="الموقع" FontWeight="Bold" FontSize="10" Foreground="#7F8C8D" Margin="0,5,0,0"/>
                                <TextBlock Text="صنعاء" FontSize="11" Margin="0,2"/>
                                <TextBlock Text="رقم الزيارة" FontWeight="Bold" FontSize="10" Foreground="#7F8C8D" Margin="0,5,0,0"/>
                                <TextBlock Text="911-1300" FontSize="11" Margin="0,2"/>
                            </StackPanel>
                        </Border>

                        <!-- زيارة 2 -->
                        <Border Background="#E8F6F3" BorderBrush="#27AE60" BorderThickness="1"
                                Margin="0,5" Padding="10" CornerRadius="3">
                            <StackPanel>
                                <TextBlock Text="التاريخ" FontWeight="Bold" FontSize="10" Foreground="#27AE60"/>
                                <TextBlock Text="22/07/2025 الموافق" FontSize="11" Margin="0,2"/>
                                <TextBlock Text="الموقع" FontWeight="Bold" FontSize="10" Foreground="#27AE60" Margin="0,5,0,0"/>
                                <TextBlock Text="تعز وذمار" FontSize="11" Margin="0,2"/>
                                <TextBlock Text="رقم الزيارة" FontWeight="Bold" FontSize="10" Foreground="#27AE60" Margin="0,5,0,0"/>
                                <TextBlock Text="911-1310" FontSize="11" Margin="0,2"/>
                            </StackPanel>
                        </Border>

                        <!-- زيارة 3 -->
                        <Border Background="#ECF0F1" BorderBrush="#BDC3C7" BorderThickness="1"
                                Margin="0,5" Padding="10" CornerRadius="3">
                            <StackPanel>
                                <TextBlock Text="التاريخ" FontWeight="Bold" FontSize="10" Foreground="#7F8C8D"/>
                                <TextBlock Text="23/07/2025 الموافق" FontSize="11" Margin="0,2"/>
                                <TextBlock Text="الموقع" FontWeight="Bold" FontSize="10" Foreground="#7F8C8D" Margin="0,5,0,0"/>
                                <TextBlock Text="زيارة ميدانية" FontSize="11" Margin="0,2"/>
                                <TextBlock Text="رقم الزيارة" FontWeight="Bold" FontSize="10" Foreground="#7F8C8D" Margin="0,5,0,0"/>
                                <TextBlock Text="911-1333" FontSize="11" Margin="0,2"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </ScrollViewer>
            </Grid>
        </Border>

    </Grid>

</Window>
