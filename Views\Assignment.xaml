<Window x:Class="DriverManagementSystem.Views.AssignmentWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="التكليف - نظام إدارة الزيارات الميدانية"
        Height="700"
        Width="900"
        MinHeight="600"
        MinWidth="800"
        WindowStartupLocation="CenterScreen"
        Background="#F5F5F5"
        FlowDirection="RightToLeft"
        FontFamily="Arial"
        ResizeMode="CanResize">

    <!-- تخطيط معكوس - الإطار الجانبي يسار والمحتوى يمين -->
    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="300"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- Header الأزرق مع الأزرار -->
        <Border Grid.Row="0" Grid.ColumnSpan="2" Background="#2E86C1" Padding="15,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- الأزرار اليسار -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Button Content="❌ إغلاق" Click="CloseButton_Click"
                          Background="#E74C3C" Foreground="White" Padding="15,8" Margin="5,0"
                          FontSize="12" FontWeight="Bold" BorderThickness="0"/>
                    <Button Content="📋 التكليف"
                          Background="#34495E" Foreground="White" Padding="15,8" Margin="5,0"
                          FontSize="12" FontWeight="Bold" BorderThickness="0"/>
                    <Button Content="📄 توثيق الرسائل"
                          Background="#27AE60" Foreground="White" Padding="15,8" Margin="5,0"
                          FontSize="12" FontWeight="Bold" BorderThickness="0"/>
                </StackPanel>

                <!-- العنوان الوسط -->
                <TextBlock Grid.Column="1" Text="تقرير النزول الميداني"
                         FontSize="18" FontWeight="Bold" Foreground="White"
                         HorizontalAlignment="Center" VerticalAlignment="Center"/>

                <!-- أيقونة التقرير -->
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <TextBlock Text="📋" FontSize="24" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- الإطار الجانبي الأيسر -->
        <Border Grid.Row="1" Grid.Column="0" Background="White" BorderBrush="#BDC3C7"
                BorderThickness="0,0,2,0" Margin="20,20,0,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- عنوان الإطار الجانبي -->
                <Border Grid.Row="0" Background="#3498DB" Padding="15,10">
                    <StackPanel>
                        <TextBlock Text="📋 رقم الزيارة" FontSize="14" FontWeight="Bold"
                                   Foreground="White" HorizontalAlignment="Center"/>
                        <TextBlock Text="🔢" FontSize="20" Foreground="White"
                                   HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <!-- رقم الزيارة فقط -->
                <Border Grid.Row="1" Background="#ECF0F1" Margin="10" Padding="15" CornerRadius="5">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <TextBlock Text="رقم الزيارة" FontWeight="Bold" FontSize="12"
                                   Foreground="#7F8C8D" HorizontalAlignment="Center"/>
                        <TextBlock Text="{Binding SelectedVisit.VisitNumber, FallbackValue='911-1300'}"
                                   FontSize="24" FontWeight="Bold" Foreground="#2C3E50"
                                   HorizontalAlignment="Center" Margin="0,10"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>

        <!-- المحتوى الرئيسي - المنطقة الفارغة -->
        <Grid Grid.Row="1" Grid.Column="1" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- زر الطباعة فقط -->
            <Border Grid.Row="0" Background="#ECF0F1" Padding="10" Margin="0,0,0,10" CornerRadius="5">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button Name="PrintButton" Content="🖨️ طباعة" Click="PrintButton_Click"
                          Background="#27AE60" Foreground="White" Padding="20,10"
                          FontSize="14" FontWeight="Bold" BorderThickness="0"/>
                </StackPanel>
            </Border>

            <!-- منطقة التصميم الطولية -->
            <Border Grid.Row="1" BorderBrush="#BDC3C7" BorderThickness="2" Background="White" CornerRadius="5">
                <ScrollViewer Name="PrintArea" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Hidden"
                             HorizontalAlignment="Stretch" VerticalAlignment="Stretch" Padding="0">
                    <Grid Background="White" HorizontalAlignment="Stretch" VerticalAlignment="Top">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- الصفحة الكاملة بحجم A4 -->
                        <StackPanel Grid.Row="0" Background="White" Margin="15,5,5,5" HorizontalAlignment="Stretch">

                            <!-- الفوتر الثابت -->
                            <Border BorderBrush="#CCCCCC" BorderThickness="2" Padding="20,15,15,15" Margin="0,0,0,20" HorizontalAlignment="Stretch">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- النص الإنجليزي اليسار -->
                                    <StackPanel Grid.Column="0" HorizontalAlignment="Left">
                                        <TextBlock Text="Social Fund For Development" FontWeight="Bold" FontSize="12" Foreground="#7F8C8D"/>
                                        <TextBlock Text="Republic OF YEMEN" FontWeight="Bold" FontSize="12" Foreground="#7F8C8D"/>
                                        <Border BorderBrush="#7F8C8D" BorderThickness="1" Padding="8,4" Margin="0,5,0,0">
                                            <StackPanel>
                                                <TextBlock Text="Presidency of Council of Ministers" FontSize="10" Foreground="#7F8C8D"/>
                                                <TextBlock Text="Dhamar &amp;Albidaa Branch" FontSize="10" Foreground="#7F8C8D"/>
                                            </StackPanel>
                                        </Border>
                                    </StackPanel>

                                    <!-- الشعار الوسط -->
                                    <StackPanel Grid.Column="1" HorizontalAlignment="Center" Margin="20,0">
                                        <Border Background="#3498DB" Width="60" Height="60" CornerRadius="5">
                                            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                                                <TextBlock Text="S" FontSize="16" FontWeight="Bold" Foreground="White"/>
                                                <TextBlock Text="F" FontSize="16" FontWeight="Bold" Foreground="White"/>
                                                <TextBlock Text="D" FontSize="16" FontWeight="Bold" Foreground="White"/>
                                            </StackPanel>
                                        </Border>
                                        <TextBlock Text="الصندوق" FontSize="8" HorizontalAlignment="Center" Margin="0,2,0,0"/>
                                        <TextBlock Text="الاجتماعي" FontSize="8" HorizontalAlignment="Center"/>
                                        <TextBlock Text="للتنمية" FontSize="8" HorizontalAlignment="Center"/>
                                        <TextBlock Text="SocialFundForDevelopment" FontSize="6" HorizontalAlignment="Center" Foreground="#7F8C8D"/>
                                    </StackPanel>

                                    <!-- النص العربي اليمين -->
                                    <StackPanel Grid.Column="2" HorizontalAlignment="Right">
                                        <TextBlock Text="الجمهورية اليمنية" FontWeight="Bold" FontSize="12" HorizontalAlignment="Right"/>
                                        <TextBlock Text="رئاسة مجلس الوزراء" FontWeight="Bold" FontSize="12" HorizontalAlignment="Right"/>
                                        <Border BorderBrush="#7F8C8D" BorderThickness="1" Padding="8,4" Margin="0,5,0,0">
                                            <StackPanel>
                                                <TextBlock Text="الصندوق الاجتماعي للتنمية" FontSize="10" HorizontalAlignment="Right"/>
                                                <TextBlock Text="فرع ذمار البيضاء" FontSize="10" HorizontalAlignment="Right"/>
                                            </StackPanel>
                                        </Border>
                                    </StackPanel>
                                </Grid>
                            </Border>

                            <!-- منطقة المحتوى -->
                            <Border BorderBrush="#E0E0E0" BorderThickness="1" Background="White" MinHeight="600" Padding="30" HorizontalAlignment="Stretch">
                                <StackPanel>

                                    <!-- عنوان التكليف -->
                                    <TextBlock Text="تكليف" FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,20,0,30"/>

                                    <!-- معلومات الزيارة -->
                                    <Grid Margin="0,0,0,30">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0" HorizontalAlignment="Right">
                                            <TextBlock Text="رقم الزيارة: 001" FontSize="14" Margin="0,5"/>
                                            <TextBlock Text="تاريخ الزيارة: ___________" FontSize="14" Margin="0,5"/>
                                            <TextBlock Text="مدة الزيارة: ___________" FontSize="14" Margin="0,5"/>
                                        </StackPanel>

                                        <StackPanel Grid.Column="1" HorizontalAlignment="Right">
                                            <TextBlock Text="المشروع: ___________" FontSize="14" Margin="0,5"/>
                                            <TextBlock Text="الموقع: ___________" FontSize="14" Margin="0,5"/>
                                            <TextBlock Text="المنطقة: ___________" FontSize="14" Margin="0,5"/>
                                        </StackPanel>
                                    </Grid>

                                    <!-- جدول السائقين -->
                                    <TextBlock Text="السائقون المكلفون:" FontSize="16" FontWeight="Bold" Margin="0,20,0,10" HorizontalAlignment="Right"/>

                                    <Border BorderBrush="#CCCCCC" BorderThickness="1" Margin="0,0,0,30">
                                        <Grid>
                                            <Grid.RowDefinitions>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                                <RowDefinition Height="Auto"/>
                                            </Grid.RowDefinitions>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="50"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <!-- رأس الجدول -->
                                            <Border Grid.Row="0" Grid.Column="0" BorderBrush="#CCCCCC" BorderThickness="0,0,1,1" Background="#F5F5F5" Padding="5">
                                                <TextBlock Text="م" FontWeight="Bold" HorizontalAlignment="Center"/>
                                            </Border>
                                            <Border Grid.Row="0" Grid.Column="1" BorderBrush="#CCCCCC" BorderThickness="0,0,1,1" Background="#F5F5F5" Padding="5">
                                                <TextBlock Text="اسم السائق" FontWeight="Bold" HorizontalAlignment="Center"/>
                                            </Border>
                                            <Border Grid.Row="0" Grid.Column="2" BorderBrush="#CCCCCC" BorderThickness="0,0,1,1" Background="#F5F5F5" Padding="5">
                                                <TextBlock Text="رقم الهاتف" FontWeight="Bold" HorizontalAlignment="Center"/>
                                            </Border>
                                            <Border Grid.Row="0" Grid.Column="3" BorderBrush="#CCCCCC" BorderThickness="0,0,0,1" Background="#F5F5F5" Padding="5">
                                                <TextBlock Text="نوع المركبة" FontWeight="Bold" HorizontalAlignment="Center"/>
                                            </Border>

                                            <!-- صفوف البيانات -->
                                            <Border Grid.Row="1" Grid.Column="0" BorderBrush="#CCCCCC" BorderThickness="0,0,1,1" Padding="5">
                                                <TextBlock Text="1" HorizontalAlignment="Center"/>
                                            </Border>
                                            <Border Grid.Row="1" Grid.Column="1" BorderBrush="#CCCCCC" BorderThickness="0,0,1,1" Padding="5">
                                                <TextBlock Text="___________________" HorizontalAlignment="Center"/>
                                            </Border>
                                            <Border Grid.Row="1" Grid.Column="2" BorderBrush="#CCCCCC" BorderThickness="0,0,1,1" Padding="5">
                                                <TextBlock Text="___________________" HorizontalAlignment="Center"/>
                                            </Border>
                                            <Border Grid.Row="1" Grid.Column="3" BorderBrush="#CCCCCC" BorderThickness="0,0,0,1" Padding="5">
                                                <TextBlock Text="___________________" HorizontalAlignment="Center"/>
                                            </Border>

                                            <Border Grid.Row="2" Grid.Column="0" BorderBrush="#CCCCCC" BorderThickness="0,0,1,1" Padding="5">
                                                <TextBlock Text="2" HorizontalAlignment="Center"/>
                                            </Border>
                                            <Border Grid.Row="2" Grid.Column="1" BorderBrush="#CCCCCC" BorderThickness="0,0,1,1" Padding="5">
                                                <TextBlock Text="___________________" HorizontalAlignment="Center"/>
                                            </Border>
                                            <Border Grid.Row="2" Grid.Column="2" BorderBrush="#CCCCCC" BorderThickness="0,0,1,1" Padding="5">
                                                <TextBlock Text="___________________" HorizontalAlignment="Center"/>
                                            </Border>
                                            <Border Grid.Row="2" Grid.Column="3" BorderBrush="#CCCCCC" BorderThickness="0,0,0,1" Padding="5">
                                                <TextBlock Text="___________________" HorizontalAlignment="Center"/>
                                            </Border>

                                            <Border Grid.Row="3" Grid.Column="0" BorderBrush="#CCCCCC" BorderThickness="0,0,1,1" Padding="5">
                                                <TextBlock Text="3" HorizontalAlignment="Center"/>
                                            </Border>
                                            <Border Grid.Row="3" Grid.Column="1" BorderBrush="#CCCCCC" BorderThickness="0,0,1,1" Padding="5">
                                                <TextBlock Text="___________________" HorizontalAlignment="Center"/>
                                            </Border>
                                            <Border Grid.Row="3" Grid.Column="2" BorderBrush="#CCCCCC" BorderThickness="0,0,1,1" Padding="5">
                                                <TextBlock Text="___________________" HorizontalAlignment="Center"/>
                                            </Border>
                                            <Border Grid.Row="3" Grid.Column="3" BorderBrush="#CCCCCC" BorderThickness="0,0,0,1" Padding="5">
                                                <TextBlock Text="___________________" HorizontalAlignment="Center"/>
                                            </Border>

                                            <Border Grid.Row="4" Grid.Column="0" BorderBrush="#CCCCCC" BorderThickness="0,0,1,0" Padding="5">
                                                <TextBlock Text="4" HorizontalAlignment="Center"/>
                                            </Border>
                                            <Border Grid.Row="4" Grid.Column="1" BorderBrush="#CCCCCC" BorderThickness="0,0,1,0" Padding="5">
                                                <TextBlock Text="___________________" HorizontalAlignment="Center"/>
                                            </Border>
                                            <Border Grid.Row="4" Grid.Column="2" BorderBrush="#CCCCCC" BorderThickness="0,0,1,0" Padding="5">
                                                <TextBlock Text="___________________" HorizontalAlignment="Center"/>
                                            </Border>
                                            <Border Grid.Row="4" Grid.Column="3" BorderBrush="#CCCCCC" BorderThickness="0,0,0,0" Padding="5">
                                                <TextBlock Text="___________________" HorizontalAlignment="Center"/>
                                            </Border>
                                        </Grid>
                                    </Border>

                                    <!-- ملاحظات -->
                                    <TextBlock Text="ملاحظات:" FontSize="16" FontWeight="Bold" Margin="0,20,0,10" HorizontalAlignment="Right"/>
                                    <Border BorderBrush="#CCCCCC" BorderThickness="1" MinHeight="100" Padding="10" Margin="0,0,0,30">
                                        <TextBlock Text="________________________________________________________________________________________________________________________________________________________________________________________________________________________________________________"
                                                   TextWrapping="Wrap" FontSize="12"/>
                                    </Border>

                                    <!-- التوقيعات -->
                                    <Grid Margin="0,30,0,0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>

                                        <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                                            <TextBlock Text="توقيع المسؤول" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,30"/>
                                            <TextBlock Text="____________________" HorizontalAlignment="Center"/>
                                        </StackPanel>

                                        <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                            <TextBlock Text="التاريخ" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,30"/>
                                            <TextBlock Text="____________________" HorizontalAlignment="Center"/>
                                        </StackPanel>
                                    </Grid>

                                </StackPanel>
                            </Border>

                        </StackPanel>
                    </Grid>
                </ScrollViewer>
            </Border>
        </Grid>
    </Grid>

</Window>
