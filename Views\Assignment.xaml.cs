using System;
using System.Windows;
using System.Windows.Controls;
using DriverManagementSystem.ViewModels;
using DriverManagementSystem.Services;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Views
{
    public partial class ReportView : UserControl
    {
        public ReportView()
        {
            InitializeComponent();
            // إنشاء ViewModel افتراضي فقط إذا لم يكن هناك DataContext
            if (DataContext == null)
            {
                DataContext = new ReportViewModel();
            }

            // تحميل قيم Footer المحفوظة بعد تحميل الواجهة
            Loaded += ReportView_Loaded;
        }

        /// <summary>
        /// Constructor مع DataContext محدد مسبقاً
        /// </summary>
        public ReportView(ReportViewModel viewModel)
        {
            InitializeComponent();
            DataContext = viewModel;
        }

        /// <summary>
        /// طباعة التقرير مباشرة
        /// </summary>
        private void PrintReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                DriverManagementSystem.Services.ReportViewPrintService.PrintReportView(this, "تقرير الزيارة الميدانية");
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معاينة الطباعة
        /// </summary>
        private void PrintPreviewButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                DriverManagementSystem.Services.ReportViewPrintService.PrintReportView(this);
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في معاينة الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحرير قالب العقد
        /// </summary>
        private void EditContractButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var contractEditorWindow = new DriverManagementSystem.Views.ContractEditorWindow();
                contractEditorWindow.ShowDialog();
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة تحرير العقد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// توثيق الرسائل النصية
        /// </summary>
        private void MessageDocumentationButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على الزيارة المحددة من ViewModel
                var viewModel = DataContext as ReportViewModel;
                var selectedVisit = viewModel?.SelectedVisit;

                if (selectedVisit != null)
                {
                    // فتح نافذة توثيق الرسائل مع الزيارة المحددة
                    var messageWindow = new DriverManagementSystem.Views.PowerfulMessageDocumentationWindow(selectedVisit);
                    messageWindow.ShowDialog();
                }
                else
                {
                    // فتح نافذة توثيق الرسائل العامة
                    var messageWindow = new DriverManagementSystem.Views.PowerfulMessageDocumentationWindow();
                    messageWindow.ShowDialog();
                }
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة توثيق الرسائل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// التكليف
        /// </summary>
        private void AssignmentButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // الحصول على الزيارة المحددة من ViewModel
                var viewModel = DataContext as ReportViewModel;
                var selectedVisit = viewModel?.SelectedVisit;

                if (selectedVisit != null)
                {
                    // فتح نافذة التكليف المبسطة مع الزيارة المحددة
                    var assignmentWindow = new DriverManagementSystem.Views.SimpleAssignmentWindow(selectedVisit);
                    assignmentWindow.ShowDialog();
                }
                else
                {
                    MessageBox.Show("يرجى اختيار زيارة أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (System.Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة التكليف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث عروض الأسعار من نافذة الرسائل المهنية
        /// </summary>
        private async void RefreshOffersButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (DataContext is ReportViewModel viewModel && viewModel.SelectedVisit != null)
                {
                    // إعادة تحميل البيانات للزيارة المحددة
                    await viewModel.LoadDriversAndPricesData(viewModel.SelectedVisit);

                    MessageBox.Show($"تم تحديث عروض الأسعار\n\nعدد العروض المحملة: {viewModel.ReportData.PriceOffers.Count}",
                                  "تم التحديث", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("لا توجد زيارة ميدانية محددة للتحديث", "تنبيه",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث العروض: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالج حدث تحميل الواجهة
        /// </summary>
        private void ReportView_Loaded(object sender, RoutedEventArgs e)
        {
            LoadFooterValues();
        }

        /// <summary>
        /// تحميل قيم Footer المحفوظة
        /// </summary>
        private void LoadFooterValues()
        {
            try
            {
                // البحث عن مربعات النص باستخدام FindName
                var approverNameTextBox = FindName("ApproverNameTextBox") as TextBox;
                var approverTitleTextBox = FindName("ApproverTitleTextBox") as TextBox;
                var movementResponsibleTextBox = FindName("MovementResponsibleTextBox") as TextBox;
                var taskAssigneeTextBox = FindName("TaskAssigneeTextBox") as TextBox;

                if (approverNameTextBox != null)
                {
                    approverNameTextBox.Text = Properties.Settings.Default.ApproverName ?? "م/محمد محمد الملحمي";
                    approverTitleTextBox.Text = Properties.Settings.Default.ApproverTitle ?? "مدير الفريق";
                    movementResponsibleTextBox.Text = Properties.Settings.Default.MovementResponsible ?? "علي علي الغمدي";
                    taskAssigneeTextBox.Text = Properties.Settings.Default.TaskAssignee ?? "علي علي أحمد الغمدي";

                    // حفظ القيم عند تغييرها
                    approverNameTextBox.TextChanged += (s, e) => SaveFooterValues();
                    approverTitleTextBox.TextChanged += (s, e) => SaveFooterValues();
                    movementResponsibleTextBox.TextChanged += (s, e) => SaveFooterValues();
                    taskAssigneeTextBox.TextChanged += (s, e) => SaveFooterValues();
                }
            }
            catch (Exception ex)
            {
                // في حالة الخطأ، استخدم القيم الافتراضية
                System.Diagnostics.Debug.WriteLine($"خطأ في تحميل قيم Footer: {ex.Message}");
            }
        }

        /// <summary>
        /// حفظ قيم Footer
        /// </summary>
        private void SaveFooterValues()
        {
            try
            {
                // البحث عن مربعات النص باستخدام FindName
                var approverNameTextBox = FindName("ApproverNameTextBox") as TextBox;
                var approverTitleTextBox = FindName("ApproverTitleTextBox") as TextBox;
                var movementResponsibleTextBox = FindName("MovementResponsibleTextBox") as TextBox;
                var taskAssigneeTextBox = FindName("TaskAssigneeTextBox") as TextBox;

                if (approverNameTextBox != null)
                {
                    Properties.Settings.Default.ApproverName = approverNameTextBox.Text;
                    Properties.Settings.Default.ApproverTitle = approverTitleTextBox.Text;
                    Properties.Settings.Default.MovementResponsible = movementResponsibleTextBox.Text;
                    Properties.Settings.Default.TaskAssignee = taskAssigneeTextBox.Text;
                    Properties.Settings.Default.Save();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في حفظ قيم Footer: {ex.Message}");
            }
        }
    }
}
