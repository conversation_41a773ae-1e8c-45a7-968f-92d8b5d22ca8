using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Printing;
using DriverManagementSystem.ViewModels;
using DriverManagementSystem.Services;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// نافذة التكليف الجديدة
    /// </summary>
    public partial class AssignmentWindow : Window
    {
        private readonly FieldVisit _selectedVisit;
        private readonly AssignmentViewModel _viewModel;

        public AssignmentWindow(FieldVisit selectedVisit)
        {
            InitializeComponent();
            _selectedVisit = selectedVisit;

            // إنشاء وربط ViewModel
            _viewModel = new AssignmentViewModel(selectedVisit);
            DataContext = _viewModel;

            // تحديث العنوان
            Title = $"التكليف - زيارة {selectedVisit?.VisitNumber ?? "001"}";
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إنشاء حوار الطباعة
                PrintDialog printDialog = new PrintDialog();

                if (printDialog.ShowDialog() == true)
                {
                    // الحصول على منطقة الطباعة
                    var printArea = FindName("PrintArea") as ScrollViewer;
                    if (printArea != null)
                    {
                        // إخفاء شريط التمرير أثناء الطباعة
                        var originalVerticalScrollBarVisibility = printArea.VerticalScrollBarVisibility;
                        var originalHorizontalScrollBarVisibility = printArea.HorizontalScrollBarVisibility;

                        printArea.VerticalScrollBarVisibility = ScrollBarVisibility.Hidden;
                        printArea.HorizontalScrollBarVisibility = ScrollBarVisibility.Hidden;

                        // تحديث التخطيط
                        printArea.UpdateLayout();

                        // طباعة المحتوى
                        printDialog.PrintVisual(printArea, $"تكليف - زيارة {_selectedVisit?.VisitNumber ?? "001"}");

                        // إعادة إظهار شريط التمرير
                        printArea.VerticalScrollBarVisibility = originalVerticalScrollBarVisibility;
                        printArea.HorizontalScrollBarVisibility = originalHorizontalScrollBarVisibility;

                        MessageBox.Show("تم إرسال المستند للطباعة بنجاح!", "طباعة",
                                      MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ أثناء الطباعة: {ex.Message}", "خطأ في الطباعة",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
