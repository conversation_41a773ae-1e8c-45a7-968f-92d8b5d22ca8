using System;
using System.Windows;
using System.Windows.Controls;
using DriverManagementSystem.ViewModels;
using DriverManagementSystem.Services;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Views
{
    public partial class AssignmentWindow : Window
    {
        public AssignmentWindow()
        {
            InitializeComponent();
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                PrintDialog printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    // طباعة منطقة المحتوى فقط
                    var contentArea = this.FindName("ContentArea") as FrameworkElement;
                    if (contentArea != null)
                    {
                        printDialog.PrintVisual(contentArea, "تكليف - نظام إدارة الزيارات الميدانية");
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
