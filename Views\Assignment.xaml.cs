using System;
using System.Windows;
using System.Windows.Controls;
using DriverManagementSystem.ViewModels;
using DriverManagementSystem.Services;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// نافذة التكليف الجديدة
    /// </summary>
    public partial class AssignmentWindow : Window
    {
        private readonly FieldVisit _selectedVisit;
        private readonly AssignmentViewModel _viewModel;

        public AssignmentWindow(FieldVisit selectedVisit)
        {
            InitializeComponent();
            _selectedVisit = selectedVisit;

            // إنشاء وربط ViewModel
            _viewModel = new AssignmentViewModel(selectedVisit);
            DataContext = _viewModel;

            // تحديث العنوان
            Title = $"التكليف - زيارة {selectedVisit?.VisitNumber ?? "001"}";
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
