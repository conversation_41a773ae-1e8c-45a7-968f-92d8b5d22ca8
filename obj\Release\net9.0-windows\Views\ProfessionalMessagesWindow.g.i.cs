﻿#pragma checksum "..\..\..\..\Views\ProfessionalMessagesWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "9C590595D10AC1F2D2BF7212A7D101B9EAB51870"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using SFDSystem.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace SFDSystem.Views {
    
    
    /// <summary>
    /// ProfessionalMessagesWindow
    /// </summary>
    public partial class ProfessionalMessagesWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 239 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearSelectionButton;
        
        #line default
        #line hidden
        
        
        #line 341 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenDriverManagementButton;
        
        #line default
        #line hidden
        
        
        #line 498 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid OffersResultsGrid;
        
        #line default
        #line hidden
        
        
        #line 606 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OffersButton;
        
        #line default
        #line hidden
        
        
        #line 645 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditOffersButton;
        
        #line default
        #line hidden
        
        
        #line 707 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveOffersButton;
        
        #line default
        #line hidden
        
        
        #line 745 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearOffersButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/SFDSystem;V2.0.0.0;component/views/professionalmessageswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ClearSelectionButton = ((System.Windows.Controls.Button)(target));
            
            #line 245 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            this.ClearSelectionButton.Click += new System.Windows.RoutedEventHandler(this.ClearSelectionButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.OpenDriverManagementButton = ((System.Windows.Controls.Button)(target));
            
            #line 341 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            this.OpenDriverManagementButton.Click += new System.Windows.RoutedEventHandler(this.OpenDriverManagementButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.OffersResultsGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 5:
            this.OffersButton = ((System.Windows.Controls.Button)(target));
            
            #line 606 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            this.OffersButton.Click += new System.Windows.RoutedEventHandler(this.OffersButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.EditOffersButton = ((System.Windows.Controls.Button)(target));
            
            #line 645 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            this.EditOffersButton.Click += new System.Windows.RoutedEventHandler(this.EditOffersButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.SaveOffersButton = ((System.Windows.Controls.Button)(target));
            
            #line 707 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            this.SaveOffersButton.Click += new System.Windows.RoutedEventHandler(this.SaveOffersButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.ClearOffersButton = ((System.Windows.Controls.Button)(target));
            
            #line 745 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            this.ClearOffersButton.Click += new System.Windows.RoutedEventHandler(this.ClearOffersButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 823 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            ((System.Windows.Controls.ComboBox)(target)).SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.MessageTemplate_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 922 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            ((System.Windows.Controls.RadioButton)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SendOption_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 932 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            ((System.Windows.Controls.RadioButton)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SendOption_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 942 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            ((System.Windows.Controls.RadioButton)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SendOption_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 1063 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CopyMessage_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 4:
            
            #line 570 "..\..\..\..\Views\ProfessionalMessagesWindow.xaml"
            ((System.Windows.Controls.TextBlock)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.ToggleWinnerStatus_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

